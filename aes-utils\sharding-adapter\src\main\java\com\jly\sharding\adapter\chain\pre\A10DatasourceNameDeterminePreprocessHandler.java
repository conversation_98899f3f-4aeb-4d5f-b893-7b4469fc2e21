package com.jly.sharding.adapter.chain.pre;

import com.fb.framework.core.context.ServiceContext;
import com.jly.sharding.adapter.chain.AbstractNamedChainHandler;
import com.jly.sharding.adapter.chain.PreprocessChain;
import com.jly.sharding.adapter.dto.DatasourceNameDecisionInfo;
import com.jly.sharding.adapter.dto.InvocationInfo;
import com.jly.sharding.adapter.dto.ShardingDetermines;
import com.jly.sharding.adapter.enums.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * A10 数据库名称决策处理器
 *
 * <ul>
 *   <li><strong>获取Service信息</strong>：从ThreadLocal获取Service层设置的路由信息</li>
 *   <li><strong>路由决策</strong>：按照原有6层优先级进行决策</li>
 *   <li><strong>设置路由Key</strong>：生成最终的数据源路由Key</li>
 *   <li><strong>简洁高效</strong>：逻辑清晰，性能优异</li>
 * </ul>
 *
 * <p><strong>执行顺序</strong>：A10 (Order=10) - 唯一的路由决策处理器</p>
 */
@Slf4j
@Component
public class A10DatasourceNameDeterminePreprocessHandler extends AbstractNamedChainHandler implements PreprocessChain {

    private static final Map<String, DatasourceNameDecisionInfo> serviceDecisionInfoMap = new ConcurrentHashMap<>();
    private static final Map<String, DatasourceNameDecisionInfo> daoDecisionInfoMap = new ConcurrentHashMap<>();

    @Override
    public void process(ShardingDetermines determines) {
        // 设置原始库路由值
        DatasourceNameDecisionInfo decisionInfo = this.getDecisionInfo(determines);
        determines.getDatabaseDetermines().setRawValue(this.executeRoutingDecision(decisionInfo));
    }

    private DatasourceNameDecisionInfo getDecisionInfo(ShardingDetermines determines) {
        // 优先匹配service
        DatasourceNameDecisionInfo info = this.matchDecisionInfo(determines.getService(), serviceDecisionInfoMap);
        if (info != null) {
            return info;
        }
        return this.matchDecisionInfo(determines.getDao(), daoDecisionInfoMap);
    }

    private DatasourceNameDecisionInfo matchDecisionInfo(InvocationInfo invocation, Map<String, DatasourceNameDecisionInfo> map) {
        String className = invocation.getClassName();
        // 匹配 .*
        DatasourceNameDecisionInfo decisionInfo = map.get(className + ".*");
        if (decisionInfo != null) {
            return decisionInfo;
        }
        decisionInfo = map.get(className + "." + invocation.getInvocation());
        return decisionInfo;
    }

    /**
     * 执行路由决策（原有6层优先级逻辑）
     */
    private String executeRoutingDecision(DatasourceNameDecisionInfo decisionInfo) {

        // 未配置默认走主库
        String fbAccessNo = ServiceContext.getContext().getFbAccessNo();
        if (decisionInfo == null) {
            return DatasourceType.Master.buildKey(fbAccessNo);
        }

        // 1. Specify优先级最高
        DatasourceType datasourceType = decisionInfo.getDatasourceType();

        // 1. Specify优先级最高
        if (datasourceType.isSpecify()) {
            return decisionInfo.getDataSourceId();
        }

        // 2. Master优先级次之
        // 3. 事务强制主库（运行时事务检测）
        if (datasourceType.isMaster() || this.isInActiveTransaction()) {
            return DatasourceType.Master.buildKey(fbAccessNo);
        }

        // 3. Slave优先级次之
        if (datasourceType.isSlave()) {
            return DatasourceType.Slave.buildKey(fbAccessNo);
        }

        // 4. 未配置路由规则，默认Master
        log.warn("未配置路由规则，默认Master: {}", fbAccessNo);
        return datasourceType.buildKey(fbAccessNo);
    }

    /**
     * 检测当前是否在活跃事务中
     *
     * @return true表示在活跃事务中，false表示不在事务中
     */
    public boolean isInActiveTransaction() {
        try {
            // Spring的TransactionSynchronizationManager.isActualTransactionActive()
            // 检测当前线程是否有活跃的事务
            boolean actualTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();

            if (log.isDebugEnabled()) {
                log.debug("事务状态检测: actualTransactionActive={}", actualTransactionActive);
            }

            return actualTransactionActive;
        } catch (Exception e) {
            log.warn("检测活跃事务状态异常", e);
            return false;
        }
    }
}
